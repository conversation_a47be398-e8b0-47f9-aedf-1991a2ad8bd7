{"apps/psychology-chat": {"root": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.cache": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.inputs": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.outputs": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/psychology-chat/eslint.config.js", "@nx/eslint/plugin"], "projectType": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.build": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.cache": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.dependsOn": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.inputs": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.outputs": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.executor": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options.cwd": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options.args": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options.command": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.technologies": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.technologies.0": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.description": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help.command": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help.example": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.executor": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.cwd": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.args": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.command": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.technologies": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.technologies.0": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.description": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help.command": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help.example": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build-deps": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.build-deps.dependsOn": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.dependsOn": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.executor": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.options": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.options.command": ["apps/psychology-chat/webpack.config.js", "@nx/webpack/plugin"], "name": ["apps/psychology-chat/project.json", "nx/core/project-json"], "$schema": ["apps/psychology-chat/project.json", "nx/core/project-json"], "sourceRoot": ["apps/psychology-chat/project.json", "nx/core/project-json"], "tags": ["apps/psychology-chat/project.json", "nx/core/project-json"], "tags.type:app": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.executor": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.dependsOn": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.options": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.configurations": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.options.buildTarget": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.options.runBuildTargetDependencies": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.test": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.test.executor": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.test.outputs": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.test.options": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["apps/psychology-chat/project.json", "nx/core/project-json"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/psychology-chat-e2e": {"root": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.cache": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.inputs": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.outputs": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/psychology-chat-e2e/eslint.config.js", "@nx/eslint/plugin"], "name": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "$schema": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "projectType": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "implicitDependencies": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "implicitDependencies.psychology-chat": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e.executor": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e.outputs": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e.options": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e.dependsOn": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e.options.jestConfig": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e.options.passWithNoTests": ["apps/psychology-chat-e2e/project.json", "nx/core/project-json"], "targets.e2e.cache": ["nx.json", "nx/target-defaults"], "targets.e2e.inputs": ["nx.json", "nx/target-defaults"], "targets.e2e.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.e2e.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.e2e.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.e2e.parallelism": ["nx.json", "nx/target-defaults"]}, "libs/be-auth": {"root": ["libs/be-auth/project.json", "nx/core/project-json"], "targets": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.cache": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.inputs": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.outputs": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.executor": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.command": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["libs/be-auth/eslint.config.js", "@nx/eslint/plugin"], "name": ["libs/be-auth/project.json", "nx/core/project-json"], "$schema": ["libs/be-auth/project.json", "nx/core/project-json"], "sourceRoot": ["libs/be-auth/project.json", "nx/core/project-json"], "projectType": ["libs/be-auth/project.json", "nx/core/project-json"], "tags": ["libs/be-auth/project.json", "nx/core/project-json"], "tags.scope:lib": ["libs/be-auth/project.json", "nx/core/project-json"], "tags.type:feature": ["libs/be-auth/project.json", "nx/core/project-json"], "targets.test": ["libs/be-auth/project.json", "nx/core/project-json"], "targets.test.executor": ["libs/be-auth/project.json", "nx/core/project-json"], "targets.test.outputs": ["libs/be-auth/project.json", "nx/core/project-json"], "targets.test.options": ["libs/be-auth/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["libs/be-auth/project.json", "nx/core/project-json"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "libs/be-chat": {"root": ["libs/be-chat/project.json", "nx/core/project-json"], "targets": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.cache": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.inputs": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.outputs": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.executor": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.command": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["eslint.config.js", "@nx/eslint/plugin"], "name": ["libs/be-chat/project.json", "nx/core/project-json"], "$schema": ["libs/be-chat/project.json", "nx/core/project-json"], "sourceRoot": ["libs/be-chat/project.json", "nx/core/project-json"], "projectType": ["libs/be-chat/project.json", "nx/core/project-json"], "tags": ["libs/be-chat/project.json", "nx/core/project-json"], "tags.scope:lib": ["libs/be-chat/project.json", "nx/core/project-json"], "tags.type:feature": ["libs/be-chat/project.json", "nx/core/project-json"], "targets.test": ["libs/be-chat/project.json", "nx/core/project-json"], "targets.test.executor": ["libs/be-chat/project.json", "nx/core/project-json"], "targets.test.outputs": ["libs/be-chat/project.json", "nx/core/project-json"], "targets.test.options": ["libs/be-chat/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["libs/be-chat/project.json", "nx/core/project-json"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "libs/be-database": {"root": ["libs/be-database/project.json", "nx/core/project-json"], "targets": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.cache": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.inputs": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.outputs": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.executor": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.options.command": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["eslint.config.js", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["eslint.config.js", "@nx/eslint/plugin"], "name": ["libs/be-database/project.json", "nx/core/project-json"], "$schema": ["libs/be-database/project.json", "nx/core/project-json"], "sourceRoot": ["libs/be-database/project.json", "nx/core/project-json"], "projectType": ["libs/be-database/project.json", "nx/core/project-json"], "tags": ["libs/be-database/project.json", "nx/core/project-json"], "tags.scope:lib": ["libs/be-database/project.json", "nx/core/project-json"], "tags.type:database": ["libs/be-database/project.json", "nx/core/project-json"], "targets.test": ["libs/be-database/project.json", "nx/core/project-json"], "targets.test.executor": ["libs/be-database/project.json", "nx/core/project-json"], "targets.test.outputs": ["libs/be-database/project.json", "nx/core/project-json"], "targets.test.options": ["libs/be-database/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["libs/be-database/project.json", "nx/core/project-json"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}}