{"4002927435315138987": {"e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14392689892739138808": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17281698104005510794": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2493193965859534866": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14889621464093969908": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13755031633792233370": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13888487709661481541": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7035036227601137620": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6928743990832223512": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15340740866013527125": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7747435161519022526": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9055426443709327054": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10675268531759585765": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11893119566112010096": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15342369723599238266": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}}