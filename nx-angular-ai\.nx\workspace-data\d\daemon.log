[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.902Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\372627784d77a6d79fe2\d.sock
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.908Z - [WATCHER]: Subscribed to changes within: D:\project\nx-angular-ai (native)
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.913Z - Established a connection. Number of open connections: 1
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.916Z - Closed a connection. Number of open connections: 0
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.919Z - Established a connection. Number of open connections: 1
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.924Z - [REQUEST]: Responding to the client. Shutdown initiated
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.926Z - Done responding to the client Shutdown initiated
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.927Z - Handled FORCE_SHUTDOWN. Handling time: 0. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.927Z - Closed a connection. Number of open connections: 0
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.927Z - [WATCHER]: Stopping the watcher for D:\project\nx-angular-ai (sources)
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.928Z - [WATCHER]: Stopping the watcher for D:\project\nx-angular-ai (outputs)
[NX v20.6.0 Daemon Server] - 2025-07-08T09:17:08.928Z - Server stopped because: "Request to shutdown"
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:28.663Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\372627784d77a6d79fe2\d.sock
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:28.668Z - [WATCHER]: Subscribed to changes within: D:\project\nx-angular-ai (native)
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:28.674Z - Established a connection. Number of open connections: 1
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:28.675Z - Established a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:28.677Z - Closed a connection. Number of open connections: 1
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:28.680Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:29.500Z - Time taken for 'Load Nx Plugin: D:\project\nx-angular-ai\node_modules\nx\src\plugins\js' 814.1523ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:29.530Z - Time taken for 'Load Nx Plugin: D:\project\nx-angular-ai\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 844.1962ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:29.581Z - Time taken for 'loadDefaultNxPlugins' 896.6453999999999ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:29.810Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1126.5497ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:29.992Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:29.993Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:29.993Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.061Z - Time taken for 'loadSpecifiedNxPlugins' 1310.039ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.062Z - Established a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.062Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.063Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.063Z - Established a connection. Number of open connections: 5
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.063Z - Closed a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.063Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.072Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.085Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.086Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.086Z - Handled HASH_GLOB. Handling time: 7. Response time: 14.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.087Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.088Z - Handled GLOB. Handling time: 8. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.099Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.110Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:30.110Z - Handled HASH_GLOB. Handling time: 7. Response time: 11.
(node:21048) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:32.440Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:32.440Z - Done responding to the client handleNxWorkspaceFiles
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:32.440Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.427Z - Time taken for 'build-project-configs' 3351.5901ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.570Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.573Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.575Z - Time taken for 'total for creating and serializing project graph' 4893.867499999999ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.577Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.577Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 4894. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.592Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.592Z - Time taken for 'preTasksExecution' 1.0585000000000946ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.592Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.592Z - Handled PRE_TASKS_EXECUTION. Handling time: 2. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.703Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.705Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.705Z - Handled HASH_TASKS. Handling time: 36. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.728Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.728Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:33.728Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 3. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.665Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.667Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.669Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.672Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.673Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.679Z - Time taken for 'total for creating and serializing project graph' 0.6927000000005137ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.679Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:35.679Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:43.242Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:44.182Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:44.259Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:51.453Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:51.568Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:51.624Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:51.699Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:51.795Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:51.928Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:52.133Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:18:52.263Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.364Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.370Z - [WATCHER]: proxy.conf.json was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.472Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.473Z - [REQUEST]: proxy.conf.json
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.473Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.481Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.483Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.483Z - Time taken for 'hash changed files from watcher' 0.282100000011269ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.484Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.484Z - Handled HASH_GLOB. Handling time: 1. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.484Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.484Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.486Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.486Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.486Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.541Z - Time taken for 'build-project-configs' 58.23919999998179ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.593Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.593Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.594Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:24.594Z - Time taken for 'total execution time for createProjectGraph()' 44.998999999981606ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.636Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.637Z - [WATCHER]: project.json was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.841Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.841Z - [REQUEST]: project.json
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.841Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.868Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.874Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.875Z - Time taken for 'hash changed files from watcher' 0.7510999999940395ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.876Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.876Z - Handled HASH_GLOB. Handling time: 2. Response time: 8.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.876Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.876Z - Handled GLOB. Handling time: 4. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.881Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.881Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.882Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.931Z - Time taken for 'build-project-configs' 78.86530000000494ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.980Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.981Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:22:51.981Z - Time taken for 'total execution time for createProjectGraph()' 35.65210000000661ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:02.867Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:02.976Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.494Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.494Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.495Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.500Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.500Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.502Z - Time taken for 'total for creating and serializing project graph' 0.5376999999862164ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.506Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.506Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 6.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.516Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.516Z - Time taken for 'preTasksExecution' 0.33950000000186265ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.516Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.516Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.597Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.598Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.598Z - Handled HASH_TASKS. Handling time: 29. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.618Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.618Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:20.618Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.556Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.556Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.557Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.560Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.560Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.561Z - Time taken for 'total for creating and serializing project graph' 0.45369999995455146ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.563Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:22.563Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:23:29.011Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:25:31.015Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:25:31.049Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.382Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.382Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.384Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.398Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.398Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.400Z - Time taken for 'total for creating and serializing project graph' 0.730600000009872ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.402Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.402Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.412Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.413Z - Time taken for 'preTasksExecution' 0.3616000000038184ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.413Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.413Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.467Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.467Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.468Z - Handled HASH_TASKS. Handling time: 14. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.485Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.485Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:12.485Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.250Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.250Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.251Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.254Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.255Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.257Z - Time taken for 'total for creating and serializing project graph' 0.7321000000229105ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.261Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:14.261Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:26:20.031Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.259Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.264Z - [WATCHER]: proxy.conf.json was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.365Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.365Z - [REQUEST]: proxy.conf.json
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.365Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.376Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.387Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.387Z - Time taken for 'hash changed files from watcher' 0.2979999999515712ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.387Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.387Z - Handled HASH_GLOB. Handling time: 0. Response time: 11.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.388Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.388Z - Handled GLOB. Handling time: 8. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.396Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.401Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.401Z - Handled HASH_GLOB. Handling time: 6. Response time: 5.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.454Z - Time taken for 'build-project-configs' 80.2280000000028ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.509Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.509Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.509Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:01.510Z - Time taken for 'total execution time for createProjectGraph()' 47.77859999996144ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.331Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.333Z - [WATCHER]: proxy.conf.js was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.534Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.534Z - [REQUEST]: proxy.conf.js
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.534Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.566Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.569Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.569Z - Time taken for 'hash changed files from watcher' 0.3074999999953434ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.570Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.570Z - Handled HASH_GLOB. Handling time: 3. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.570Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.570Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.574Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.574Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.574Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.623Z - Time taken for 'build-project-configs' 57.37489999993704ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.760Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.761Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.761Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:15.761Z - Time taken for 'total execution time for createProjectGraph()' 130.2493999999715ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:26.719Z - [WATCHER]: project.json was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:26.719Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.121Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.121Z - [REQUEST]: project.json
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.121Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.134Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.142Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.142Z - Time taken for 'hash changed files from watcher' 0.5773000000044703ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.143Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.143Z - Handled HASH_GLOB. Handling time: 1. Response time: 9.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.144Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.144Z - Handled GLOB. Handling time: 5. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.147Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.148Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.148Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.207Z - Time taken for 'build-project-configs' 73.56660000002012ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.260Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.261Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:27.261Z - Time taken for 'total execution time for createProjectGraph()' 46.760699999984354ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:39.127Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:33:39.134Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.575Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.575Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.576Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.580Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.581Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.582Z - Time taken for 'total for creating and serializing project graph' 0.5308999998960644ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.586Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.587Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.604Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.604Z - Time taken for 'preTasksExecution' 0.40720000001601875ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.605Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.605Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.676Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.677Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.677Z - Handled HASH_TASKS. Handling time: 25. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.776Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.777Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:17.777Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.061Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.062Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.063Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.065Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.066Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.067Z - Time taken for 'total for creating and serializing project graph' 0.5200999999651685ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.069Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:20.070Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:34:28.298Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.727Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.727Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.849Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.849Z - [REQUEST]: src/environment.ts
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.849Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.866Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.877Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.878Z - Time taken for 'hash changed files from watcher' 0.26010000007227063ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.878Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.878Z - Handled HASH_GLOB. Handling time: 4. Response time: 12.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.878Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.878Z - Handled GLOB. Handling time: 8. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.881Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.881Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.881Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:29.945Z - Time taken for 'build-project-configs' 82.67800000007264ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:30.046Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:30.046Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:30.047Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:30.047Z - Time taken for 'total execution time for createProjectGraph()' 47.848600000026636ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:35:34.037Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.279Z - [WATCHER]: nginx.conf was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.282Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.484Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.484Z - [REQUEST]: nginx.conf
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.484Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.509Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.512Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.512Z - Time taken for 'hash changed files from watcher' 1.120399999897927ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.513Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.513Z - Handled HASH_GLOB. Handling time: 2. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.513Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.513Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.516Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.516Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.516Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.574Z - Time taken for 'build-project-configs' 74.35570000018924ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.623Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.624Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.624Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:14.624Z - Time taken for 'total execution time for createProjectGraph()' 44.26219999999739ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:28.941Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:28.943Z - [WATCHER]: nginx.conf was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.345Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.346Z - [REQUEST]: nginx.conf
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.346Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.357Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.358Z - Time taken for 'hash changed files from watcher' 0.5214000002015382ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.358Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.358Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.361Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.362Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.362Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.364Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.364Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.364Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.422Z - Time taken for 'build-project-configs' 65.3691000000108ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.468Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.469Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.469Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:29.469Z - Time taken for 'total execution time for createProjectGraph()' 39.40090000000782ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:43.547Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:43.550Z - [WATCHER]: nginx.conf was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.395Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.395Z - [REQUEST]: nginx.conf
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.395Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.492Z - Time taken for 'hash changed files from watcher' 0.40159999998286366ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.559Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.599Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.603Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.603Z - Handled GLOB. Handling time: 64. Response time: 44.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.652Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.653Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.653Z - Handled HASH_GLOB. Handling time: 38. Response time: 54.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.654Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.654Z - Handled HASH_GLOB. Handling time: 48. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.717Z - Time taken for 'build-project-configs' 221.1089000001084ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.770Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.771Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.771Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:43:44.771Z - Time taken for 'total execution time for createProjectGraph()' 46.24989999993704ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:35.078Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:35.081Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:35.634Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.684Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.684Z - [REQUEST]: src/environment.ts
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.684Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.712Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.712Z - Time taken for 'hash changed files from watcher' 0.9132999998982996ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.720Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.721Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.722Z - Handled HASH_GLOB. Handling time: 2. Response time: 10.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.726Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.726Z - Handled GLOB. Handling time: 2. Response time: 6.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.730Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.730Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.730Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.786Z - Time taken for 'build-project-configs' 88.09890000009909ms
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.847Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.848Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.848Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-08T09:45:36.848Z - Time taken for 'total execution time for createProjectGraph()' 49.31319999997504ms
[NX v20.6.0 Daemon Server] - 2025-07-08T11:52:56.411Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-08T11:52:56.432Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:05.818Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\372627784d77a6d79fe2\d.sock
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:05.829Z - [WATCHER]: Subscribed to changes within: D:\project\nx-angular-ai (native)
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:05.832Z - Established a connection. Number of open connections: 1
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:05.833Z - Established a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:05.836Z - Closed a connection. Number of open connections: 1
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:05.838Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.478Z - Time taken for 'Load Nx Plugin: D:\project\nx-angular-ai\node_modules\nx\src\plugins\package-json' 633.049ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.517Z - Time taken for 'Load Nx Plugin: D:\project\nx-angular-ai\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 671.2624999999999ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.526Z - Time taken for 'loadDefaultNxPlugins' 682.3778ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.690Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 847.3812ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.847Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.847Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.847Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.864Z - Time taken for 'loadSpecifiedNxPlugins' 1007.1821ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.865Z - Established a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.865Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.867Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.872Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.872Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.872Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.873Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.873Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.873Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.875Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.876Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.876Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.880Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.886Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:06.886Z - Handled HASH_GLOB. Handling time: 1. Response time: 6.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.402Z - Time taken for 'build-project-configs' 519.3763999999999ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.451Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.453Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.455Z - Time taken for 'total for creating and serializing project graph' 1615.2319ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.458Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.458Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1615. Response time: 5.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.468Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.469Z - Time taken for 'preTasksExecution' 0.7991000000001804ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.469Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.469Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.874Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.874Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.875Z - Handled HASH_TASKS. Handling time: 34. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.896Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.896Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:07.896Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 3. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.667Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.667Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.668Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.671Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.672Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.673Z - Time taken for 'total for creating and serializing project graph' 0.4942000000000917ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.675Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:09.675Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.105Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.559Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.630Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.630Z - Done responding to the client recordOutputsHash
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.630Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.641Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.641Z - Done responding to the client handleRecordTaskRuns
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.641Z - Handled RECORD_TASK_RUNS. Handling time: 2. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.644Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.644Z - Done responding to the client handleGetFlakyTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.644Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.651Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.652Z - Time taken for 'postTasksExecution' 0.5090000000000146ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.652Z - Done responding to the client handleRunPostTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.652Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:45:16.660Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.526Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.528Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.629Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.630Z - [REQUEST]: src/environment.ts
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.630Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.640Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.642Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.643Z - Time taken for 'hash changed files from watcher' 0.25259999999252614ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.643Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.643Z - Handled HASH_GLOB. Handling time: 3. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.643Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.643Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.645Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.645Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.645Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.808Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:45.809Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:46.011Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:46.011Z - [REQUEST]: src/environment.ts
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:46.011Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:46.024Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:46.026Z - Time taken for 'hash changed files from watcher' 0.25560000000405125ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:46.026Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:46.026Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.613Z - Time taken for 'build-project-configs' 2591.9421999999904ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.615Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.616Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.616Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.618Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.618Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.618Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.630Z - Time taken for 'build-project-configs' 2610.3148999999976ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.698Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.699Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.699Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.699Z - Time taken for 'total execution time for createProjectGraph()' 49.76380000000063ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.726Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.727Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.727Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:46:48.727Z - Time taken for 'total execution time for createProjectGraph()' 86.41879999999946ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.929Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.929Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.930Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.934Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.934Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.935Z - Time taken for 'total for creating and serializing project graph' 0.5142999999807216ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.938Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.938Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.957Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.957Z - Time taken for 'preTasksExecution' 0.3558999999659136ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.957Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:22.957Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:23.028Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:23.029Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:23.029Z - Handled HASH_TASKS. Handling time: 25. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:23.045Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:23.046Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:23.046Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.538Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.539Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.539Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.542Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.542Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.543Z - Time taken for 'total for creating and serializing project graph' 0.4322999999858439ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.546Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:24.546Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:29.947Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.660Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.662Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.763Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.763Z - [REQUEST]: src/environment.ts
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.763Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.773Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.775Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.776Z - Time taken for 'hash changed files from watcher' 0.22499999997671694ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.776Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.776Z - Handled HASH_GLOB. Handling time: 0. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.776Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.776Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.778Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.778Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.778Z - Handled HASH_GLOB. Handling time: 0. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.796Z - Time taken for 'build-project-configs' 25.052900000009686ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.846Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.846Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.846Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:51.847Z - Time taken for 'total execution time for createProjectGraph()' 40.45899999997346ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:51:52.940Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.668Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.668Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.871Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.871Z - [REQUEST]: src/environment.ts
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.871Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.906Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.909Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.909Z - Time taken for 'hash changed files from watcher' 0.5625999999465421ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.913Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.913Z - Handled HASH_GLOB. Handling time: 14. Response time: 7.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.914Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.914Z - Handled GLOB. Handling time: 2. Response time: 5.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.918Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.920Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.920Z - Handled HASH_GLOB. Handling time: 3. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.962Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.973Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:09.984Z - Time taken for 'build-project-configs' 93.58310000004712ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.042Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.043Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.043Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.043Z - Time taken for 'total execution time for createProjectGraph()' 47.93969999998808ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.134Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.142Z - [WATCHER]: src/environment.ts was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.375Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.375Z - [REQUEST]: src/environment.ts
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.375Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.434Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.438Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.438Z - Time taken for 'hash changed files from watcher' 0.21470000001136214ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.443Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.443Z - Handled HASH_GLOB. Handling time: 46. Response time: 9.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.443Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.443Z - Handled GLOB. Handling time: 3. Response time: 5.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.463Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.464Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.464Z - Handled HASH_GLOB. Handling time: 18. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.474Z - Time taken for 'build-project-configs' 85.68949999997858ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.517Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.518Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.518Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.518Z - Time taken for 'total execution time for createProjectGraph()' 32.71019999997225ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:10.911Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.162Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.229Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.229Z - Done responding to the client recordOutputsHash
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.229Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.237Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.238Z - Done responding to the client handleRecordTaskRuns
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.239Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.242Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.242Z - Done responding to the client handleGetFlakyTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.242Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.249Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.249Z - Time taken for 'postTasksExecution' 0.5050000000046566ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.250Z - Done responding to the client handleRunPostTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.250Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:11.256Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.703Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.704Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.704Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.711Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.712Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.713Z - Time taken for 'total for creating and serializing project graph' 0.707799999974668ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.715Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.716Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.725Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.725Z - Time taken for 'preTasksExecution' 0.3576999999349937ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.725Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.725Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.794Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.795Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.795Z - Handled HASH_TASKS. Handling time: 24. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.812Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.813Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:20.813Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.681Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.682Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.682Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.685Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.685Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.687Z - Time taken for 'total for creating and serializing project graph' 0.43640000000596046ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.689Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:22.689Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:28.635Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.094Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.163Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.163Z - Done responding to the client recordOutputsHash
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.163Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.171Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.171Z - Done responding to the client handleRecordTaskRuns
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.171Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.173Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.174Z - Done responding to the client handleGetFlakyTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.174Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.180Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.180Z - Time taken for 'postTasksExecution' 0.4096999999601394ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.180Z - Done responding to the client handleRunPostTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.180Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:29.184Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.952Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.953Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.954Z - Established a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.961Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.962Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.964Z - Time taken for 'total for creating and serializing project graph' 0.8524000000907108ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.966Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.966Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.976Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.976Z - Time taken for 'preTasksExecution' 0.40560000005643815ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.976Z - Done responding to the client handleRunPreTasksExecution
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:57.976Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:58.031Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:58.031Z - Done responding to the client handleHashTasks
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:58.031Z - Handled HASH_TASKS. Handling time: 14. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:58.051Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:58.052Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:58.052Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.549Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.550Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.551Z - Established a connection. Number of open connections: 4
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.554Z - [REQUEST]: Client Request for Project Graph Received
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.554Z - [REQUEST]: Responding to the client. project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.555Z - Time taken for 'total for creating and serializing project graph' 0.4510999999474734ms
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.557Z - Done responding to the client project-graph
[NX v20.6.0 Daemon Server] - 2025-07-09T06:54:59.557Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-09T06:55:04.743Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.552Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.590Z - [WATCHER]: nginx.conf was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.696Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.696Z - [REQUEST]: nginx.conf
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.696Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.727Z - Time taken for 'hash changed files from watcher' 1.026300000026822ms
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.730Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.732Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.733Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.733Z - Handled HASH_GLOB. Handling time: 1. Response time: 3.
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.734Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.734Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.736Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.737Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.737Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.788Z - Time taken for 'build-project-configs' 74.47099999990314ms
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.842Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.842Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.843Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.843Z - Time taken for 'total execution time for createProjectGraph()' 49.098500000312924ms
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.844Z - [WATCHER]: nginx.conf was modified
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:05.845Z - [WATCHER]: Processing file changes in outputs
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.049Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.049Z - [REQUEST]: nginx.conf
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.049Z - [REQUEST]: 
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.069Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.074Z - [REQUEST]: Responding to the client. handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.075Z - Time taken for 'hash changed files from watcher' 1.0086999996565282ms
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.075Z - Done responding to the client handleHashGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.075Z - Handled HASH_GLOB. Handling time: 0. Response time: 6.
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.076Z - Done responding to the client handleGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.076Z - Handled GLOB. Handling time: 3. Response time: 2.
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.079Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.079Z - Done responding to the client handleHashMultiGlob
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.079Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.099Z - Time taken for 'build-project-configs' 39.864400000311434ms
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.141Z - [SYNC]: collect registered sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.141Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.142Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v20.6.0 Daemon Server] - 2025-07-09T07:50:06.142Z - Time taken for 'total execution time for createProjectGraph()' 34.31030000001192ms
[NX v20.6.0 Daemon Server] - 2025-07-09T11:39:32.401Z - Closed a connection. Number of open connections: 3
[NX v20.6.0 Daemon Server] - 2025-07-09T11:39:32.535Z - Closed a connection. Number of open connections: 2
[NX v20.6.0 Daemon Server] - 2025-07-09T11:39:32.538Z - Closed a connection. Number of open connections: 1
[NX v20.6.0 Daemon Server] - 2025-07-09T11:39:32.539Z - Closed a connection. Number of open connections: 0
[NX v20.6.0 Daemon Server] - 2025-07-09T11:39:32.571Z - [WATCHER]: Stopping the watcher for D:\project\nx-angular-ai (sources)
[NX v20.6.0 Daemon Server] - 2025-07-09T11:39:32.574Z - [WATCHER]: Stopping the watcher for D:\project\nx-angular-ai (outputs)
[NX v20.6.0 Daemon Server] - 2025-07-09T11:39:32.738Z - Server stopped because: "10800000ms of inactivity"
