[NX Daemon Server] - 2025-07-07T08:49:14.479Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\df8e34c1cf49d3f47b5e\d.sock
[NX Daemon Server] - 2025-07-07T08:49:14.485Z - [WATCHER]: Subscribed to changes within: D:\project\experiments (native)
[NX Daemon Server] - 2025-07-07T08:49:14.533Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:49:14.534Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-07T08:49:14.541Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:49:14.544Z - [REQUEST]: Responding to the client. Shutdown initiated
[NX Daemon Server] - 2025-07-07T08:49:14.560Z - Done responding to the client Shutdown initiated
[NX Daemon Server] - 2025-07-07T08:49:14.560Z - Handled FORCE_SHUTDOWN. Handling time: 0. Response time: 16.
[NX Daemon Server] - 2025-07-07T08:49:14.561Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-07T08:49:14.561Z - [WATCHER]: Stopping the watcher for D:\project\experiments (sources)
[NX Daemon Server] - 2025-07-07T08:49:14.562Z - [WATCHER]: Stopping the watcher for D:\project\experiments (outputs)
[NX Daemon Server] - 2025-07-07T08:49:14.562Z - Server stopped because: "Request to shutdown"
[NX Daemon Server] - 2025-07-07T08:51:10.693Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\df8e34c1cf49d3f47b5e\d.sock
[NX Daemon Server] - 2025-07-07T08:51:10.697Z - [WATCHER]: Subscribed to changes within: D:\project\experiments (native)
[NX Daemon Server] - 2025-07-07T08:51:10.706Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:51:10.709Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-07T08:51:10.710Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:51:10.713Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:11.864Z - Time taken for 'Load Nx Plugin: D:\project\experiments\node_modules\nx\src\plugins\js' 1144.3262ms
[NX Daemon Server] - 2025-07-07T08:51:11.936Z - Time taken for 'loadDefaultNxPlugins' 1217.0465ms
[NX Daemon Server] - 2025-07-07T08:51:12.018Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1302.3625ms
[NX Daemon Server] - 2025-07-07T08:51:12.229Z - Time taken for 'Load Nx Plugin: @nx/webpack/plugin' 1511.5710000000001ms
[NX Daemon Server] - 2025-07-07T08:51:12.455Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-07T08:51:12.455Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-07T08:51:12.455Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-07T08:51:12.496Z - Time taken for 'loadSpecifiedNxPlugins' 1737.2226ms
[NX Daemon Server] - 2025-07-07T08:51:12.498Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-07T08:51:12.498Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-07T08:51:12.548Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-07T08:51:12.586Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Handled GLOB. Handling time: 1. Response time: 47.
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Handled HASH_GLOB. Handling time: 35. Response time: 9.
[NX Daemon Server] - 2025-07-07T08:51:13.047Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.047Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.047Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:13.050Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.051Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.051Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.054Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.055Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.055Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.059Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.060Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.061Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.063Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.064Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.064Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.066Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.067Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.067Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.107Z - Time taken for 'build-project-configs' 596.3586ms
[NX Daemon Server] - 2025-07-07T08:51:13.165Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-07T08:51:13.169Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:13.171Z - Time taken for 'total for creating and serializing project graph' 2454.1137ms
[NX Daemon Server] - 2025-07-07T08:51:13.173Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:13.173Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2455. Response time: 6.
[NX Daemon Server] - 2025-07-07T08:51:13.189Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:13.189Z - Time taken for 'preTasksExecution' 2.1044999999999163ms
[NX Daemon Server] - 2025-07-07T08:51:13.189Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:13.190Z - Handled PRE_TASKS_EXECUTION. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.308Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:13.309Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:13.309Z - Handled HASH_TASKS. Handling time: 34. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.332Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:13.333Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:13.333Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:23.401Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-07T08:51:23.472Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:23.472Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:23.473Z - Handled RECORD_OUTPUTS_HASH. Handling time: 5. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:24.247Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:24.256Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-07T08:51:24.257Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:24.260Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:24.261Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:24.263Z - Time taken for 'total for creating and serializing project graph' 0.7312000000001717ms
[NX Daemon Server] - 2025-07-07T08:51:24.264Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:24.265Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-07T08:51:24.274Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:25.218Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:25.219Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:25.221Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:25.226Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:25.227Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:25.228Z - Time taken for 'total for creating and serializing project graph' 0.5485000000007858ms
[NX Daemon Server] - 2025-07-07T08:51:25.230Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:25.231Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - Time taken for 'preTasksExecution' 0.42189999999936845ms
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.332Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:25.333Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:25.333Z - Handled HASH_TASKS. Handling time: 16. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:25.362Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:25.362Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:25.362Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.375Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:25.375Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:25.375Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 4. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.381Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:25.382Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:25.382Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:25.389Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:25.389Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:25.389Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.392Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:25.393Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:25.393Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - Time taken for 'postTasksExecution' 0.6401000000005297ms
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.539Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:54.017Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:54.018Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:54.022Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:54.043Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:54.044Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:54.046Z - Time taken for 'total for creating and serializing project graph' 0.6617999999944004ms
[NX Daemon Server] - 2025-07-07T08:51:54.048Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:54.048Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-07T08:51:54.063Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.063Z - Time taken for 'preTasksExecution' 0.39849999999569263ms
[NX Daemon Server] - 2025-07-07T08:51:54.064Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.064Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.154Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:54.155Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:54.155Z - Handled HASH_TASKS. Handling time: 18. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.181Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:54.181Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:54.182Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.198Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:54.199Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:54.199Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 4. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.209Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:54.210Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:54.210Z - Handled RECORD_OUTPUTS_HASH. Handling time: 7. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.217Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:54.218Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:54.218Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.221Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:54.222Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:54.222Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.232Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.232Z - Time taken for 'postTasksExecution' 0.39270000000396976ms
[NX Daemon Server] - 2025-07-07T08:51:54.233Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.233Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.239Z - Closed a connection. Number of open connections: 5
