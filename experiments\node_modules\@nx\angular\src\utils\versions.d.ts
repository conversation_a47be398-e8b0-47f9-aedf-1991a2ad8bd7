export declare const nxVersion: any;
export declare const angularVersion = "~19.1.0";
export declare const angularDevkitVersion = "~19.1.0";
export declare const ngPackagrVersion = "~19.1.0";
export declare const ngrxVersion = "^19.0.0";
export declare const rxjsVersion = "~7.8.0";
export declare const zoneJsVersion = "~0.15.0";
export declare const angularJsVersion = "1.7.9";
export declare const tsLibVersion = "^2.3.0";
export declare const corsVersion = "~2.8.5";
export declare const typesCorsVersion = "~2.8.5";
export declare const expressVersion = "^4.21.2";
export declare const typesExpressVersion = "^4.17.21";
export declare const browserSyncVersion = "^3.0.0";
export declare const moduleFederationNodeVersion = "^2.6.21";
export declare const moduleFederationEnhancedVersion = "^0.8.8";
export declare const angularEslintVersion = "^19.0.2";
export declare const typescriptEslintVersion = "^7.16.0";
export declare const tailwindVersion = "^3.0.2";
export declare const postcssVersion = "^8.4.5";
export declare const postcssUrlVersion = "~10.1.3";
export declare const autoprefixerVersion = "^10.4.0";
export declare const tsNodeVersion = "10.9.1";
export declare const jestPresetAngularVersion = "~14.4.0";
export declare const typesNodeVersion = "18.16.9";
export declare const jasmineMarblesVersion = "^0.9.2";
export declare const jsoncEslintParserVersion = "^2.1.0";
