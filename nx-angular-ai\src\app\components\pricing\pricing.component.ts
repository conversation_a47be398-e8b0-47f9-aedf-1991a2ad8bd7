import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NavbarComponent } from '../landing/navbar/navbar.component';
import { FooterComponent } from '../landing/footer/footer.component';
import { SafepayService } from '../../services/safepay.service';

@Component({
  selector: 'app-pricing',
  standalone: true,
  imports: [CommonModule, RouterModule, NavbarComponent, FooterComponent],
  template: `
    <div class="min-h-screen bg-gray-50">
      <app-navbar></app-navbar>
      
      <main class="pt-20">
        <!-- Header Section -->
        <section class="py-16 bg-gradient-to-br from-blue-600 to-purple-600 text-white">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
              Choose Your Perfect Plan
            </h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
              Get the mental health support you deserve with our flexible pricing options. 
              All plans include 24/7 AI support and complete privacy.
            </p>
          </div>
        </section>

        <!-- Billing Toggle -->
        <section class="py-8">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-center items-center space-x-4">
              <span class="text-gray-600" [class.text-gray-900]="!isYearly">Monthly</span>
              <button (click)="toggleBilling()" 
                      class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      [class.bg-blue-600]="isYearly"
                      [class.bg-gray-200]="!isYearly">
                <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
                      [class.translate-x-6]="isYearly"
                      [class.translate-x-1]="!isYearly"></span>
              </button>
              <span class="text-gray-600" [class.text-gray-900]="isYearly">
                Yearly 
                <span class="text-green-600 text-sm font-medium">(Save 20%)</span>
              </span>
            </div>
          </div>
        </section>

        <!-- Pricing Cards -->
        <section class="py-16">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              
              <!-- Basic Plan -->
              <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 relative">
                <div class="text-center">
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">Basic</h3>
                  <div class="mb-6">
                    <span class="text-4xl font-bold text-gray-900">
                      ${{ isYearly ? basicPlan.yearlyPrice : basicPlan.monthlyPrice }}
                    </span>
                    <span class="text-gray-600">{{ isYearly ? '/year' : '/month' }}</span>
                  </div>
                  <ul class="space-y-4 mb-8 text-left">
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      50 AI conversations per month
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Basic mood tracking
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Email support
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Mobile app access
                    </li>
                  </ul>
                  <button (click)="selectPlan('basic')" 
                          class="w-full bg-gray-900 hover:bg-gray-800 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    Get Started
                  </button>
                </div>
              </div>

              <!-- Intermediate Plan (Popular) -->
              <div class="bg-white rounded-2xl shadow-xl border-2 border-blue-500 p-8 relative transform scale-105">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
                <div class="text-center">
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">Intermediate</h3>
                  <div class="mb-6">
                    <span class="text-4xl font-bold text-gray-900">
                      ${{ isYearly ? intermediatePlan.yearlyPrice : intermediatePlan.monthlyPrice }}
                    </span>
                    <span class="text-gray-600">{{ isYearly ? '/year' : '/month' }}</span>
                  </div>
                  <ul class="space-y-4 mb-8 text-left">
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      200 AI conversations per month
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Advanced mood & progress tracking
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Priority support
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Personalized insights
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Export conversation history
                    </li>
                  </ul>
                  <button (click)="selectPlan('intermediate')" 
                          class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    Get Started
                  </button>
                </div>
              </div>

              <!-- Advanced Plan -->
              <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 relative">
                <div class="text-center">
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">Advanced</h3>
                  <div class="mb-6">
                    <span class="text-4xl font-bold text-gray-900">
                      ${{ isYearly ? advancedPlan.yearlyPrice : advancedPlan.monthlyPrice }}
                    </span>
                    <span class="text-gray-600">{{ isYearly ? '/year' : '/month' }}</span>
                  </div>
                  <ul class="space-y-4 mb-8 text-left">
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Unlimited AI conversations
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Advanced analytics & reports
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      24/7 priority support
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Custom AI personality
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      API access for integrations
                    </li>
                    <li class="flex items-center">
                      <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      White-label options
                    </li>
                  </ul>
                  <button (click)="selectPlan('advanced')" 
                          class="w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    Get Started
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- FAQ Section -->
        <section class="py-16 bg-white">
          <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">
              Frequently Asked Questions
            </h2>
            <div class="space-y-6">
              <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  Can I change my plan anytime?
                </h3>
                <p class="text-gray-600">
                  Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                </p>
              </div>
              <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  Is there a free trial?
                </h3>
                <p class="text-gray-600">
                  Yes, all plans come with a 7-day free trial. No credit card required to start.
                </p>
              </div>
              <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  How secure is my data?
                </h3>
                <p class="text-gray-600">
                  Your data is encrypted end-to-end and stored securely. We never share your personal information with third parties.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <app-footer></app-footer>
    </div>
  `,
  styleUrls: ['./pricing.component.scss']
})
export class PricingComponent {
  isYearly = false;

  basicPlan = {
    monthlyPrice: 9,
    yearlyPrice: 72 // 20% discount
  };

  intermediatePlan = {
    monthlyPrice: 19,
    yearlyPrice: 152 // 20% discount
  };

  advancedPlan = {
    monthlyPrice: 39,
    yearlyPrice: 312 // 20% discount
  };

  constructor(private safepayService: SafepayService) {}

  toggleBilling() {
    this.isYearly = !this.isYearly;
  }

  selectPlan(planType: string) {
    const plan = this.getPlanDetails(planType);
    this.safepayService.createPayment(plan);
  }

  private getPlanDetails(planType: string) {
    const plans = {
      basic: {
        name: 'Basic Plan',
        amount: this.isYearly ? this.basicPlan.yearlyPrice : this.basicPlan.monthlyPrice,
        currency: 'USD',
        interval: this.isYearly ? 'yearly' : 'monthly'
      },
      intermediate: {
        name: 'Intermediate Plan',
        amount: this.isYearly ? this.intermediatePlan.yearlyPrice : this.intermediatePlan.monthlyPrice,
        currency: 'USD',
        interval: this.isYearly ? 'yearly' : 'monthly'
      },
      advanced: {
        name: 'Advanced Plan',
        amount: this.isYearly ? this.advancedPlan.yearlyPrice : this.advancedPlan.monthlyPrice,
        currency: 'USD',
        interval: this.isYearly ? 'yearly' : 'monthly'
      }
    };
    return plans[planType as keyof typeof plans];
  }
}
