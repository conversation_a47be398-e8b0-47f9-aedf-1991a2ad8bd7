{"run": {"command": "nx serve --port 4200 --proxy-config proxy.conf.json", "startTime": "2025-07-09T06:54:20.798Z", "endTime": "2025-07-09T06:54:29.164Z", "inner": false}, "tasks": [{"taskId": "nx-angular-ai:serve:development", "target": "serve", "projectName": "nx-angular-ai", "hash": "13912223022354871438", "startTime": "2025-07-09T06:54:20.817Z", "endTime": "2025-07-09T06:54:29.161Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}