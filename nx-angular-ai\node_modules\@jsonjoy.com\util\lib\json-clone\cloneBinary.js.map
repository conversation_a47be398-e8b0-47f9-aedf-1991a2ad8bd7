{"version": 3, "file": "cloneBinary.js", "sourceRoot": "", "sources": ["../../src/json-clone/cloneBinary.ts"], "names": [], "mappings": ";;;AAAA,0DAAqD;AAErD,MAAM,EAAC,OAAO,EAAC,GAAG,KAAK,CAAC;AACxB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;AAQxB,MAAM,WAAW,GAAG,CAAc,GAAM,EAAK,EAAE;IACpD,IAAI,CAAC,GAAG;QAAE,OAAO,GAAG,CAAC;IACrB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACjB,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;YAAE,GAAG,CAAC,IAAI,CAAC,IAAA,mBAAW,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,OAAO,GAAmB,CAAC;IAC7B,CAAC;SAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACnC,IAAI,IAAA,2BAAY,EAAC,GAAG,CAAC;YAAE,OAAO,IAAI,UAAU,CAAC,GAAG,CAAiB,CAAC;QAClE,MAAM,IAAI,GAAG,UAAU,CAAC,GAAI,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,SAAS,GAAQ,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,SAAS,CAAC,GAAG,CAAC,GAAG,IAAA,mBAAW,EAAE,GAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAnBW,QAAA,WAAW,eAmBtB"}