{"version": 3, "file": "throwIfEmpty.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/throwIfEmpty.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAsChE,MAAM,UAAU,YAAY,CAAI,eAA0B,mBAAmB;IAC3E,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;QACpC,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,CAAC,KAAK,EAAE,EAAE;YACR,QAAQ,GAAG,IAAI,CAAC;YAChB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC,EACD,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAC5E,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB;IAC1B,OAAO,IAAI,UAAU,EAAE,CAAC;AAC1B,CAAC"}