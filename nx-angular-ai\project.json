{"name": "nx-angular-ai", "$schema": "node_modules/nx/schemas/project-schema.json", "includedScripts": [], "projectType": "application", "prefix": "app", "sourceRoot": "./src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/nx-angular-ai", "index": "./src/index.html", "browser": "./src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["./src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "nx-angular-ai:build:production"}, "development": {"buildTarget": "nx-angular-ai:build:development", "proxyConfig": "proxy.conf.js"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "nx-angular-ai:build"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["./src"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "nx-angular-ai:build", "port": 4200, "staticFilePath": "dist/nx-angular-ai/browser", "spa": true}}}}