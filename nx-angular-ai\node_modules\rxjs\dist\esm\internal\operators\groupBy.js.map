{"version": 3, "file": "groupBy.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/groupBy.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAuIpF,MAAM,UAAU,OAAO,CACrB,WAA4B,EAC5B,gBAAgH,EAChH,QAAyE,EACzE,SAAkC;IAElC,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;QACpC,IAAI,OAAqC,CAAC;QAC1C,IAAI,CAAC,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;YAC/D,OAAO,GAAG,gBAAyC,CAAC;SACrD;aAAM;YACL,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,gBAAgB,CAAC,CAAC;SACvD;QAGD,MAAM,MAAM,GAAG,IAAI,GAAG,EAAuB,CAAC;QAG9C,MAAM,MAAM,GAAG,CAAC,EAAkC,EAAE,EAAE;YACpD,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnB,EAAE,CAAC,UAAU,CAAC,CAAC;QACjB,CAAC,CAAC;QAIF,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAG5E,IAAI,YAAY,GAAG,CAAC,CAAC;QAGrB,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAS9B,MAAM,uBAAuB,GAAG,IAAI,kBAAkB,CACpD,UAAU,EACV,CAAC,KAAQ,EAAE,EAAE;YAIX,IAAI;gBACF,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBAE/B,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC5B,IAAI,CAAC,KAAK,EAAE;oBAEV,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,EAAO,CAAC,CAAC,CAAC;oBAKxE,MAAM,OAAO,GAAG,uBAAuB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBACpD,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAEzB,IAAI,QAAQ,EAAE;wBACZ,MAAM,kBAAkB,GAAG,wBAAwB,CAMjD,KAAY,EACZ,GAAG,EAAE;4BAGH,KAAM,CAAC,QAAQ,EAAE,CAAC;4BAClB,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,WAAW,EAAE,CAAC;wBACpC,CAAC,EAED,SAAS,EAGT,SAAS,EAET,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CACzB,CAAC;wBAGF,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC;qBACzF;iBACF;gBAGD,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aAC9C;YAAC,OAAO,GAAG,EAAE;gBACZ,WAAW,CAAC,GAAG,CAAC,CAAC;aAClB;QACH,CAAC,EAED,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAE/C,WAAW,EAKX,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,EACpB,GAAG,EAAE;YACH,iBAAiB,GAAG,IAAI,CAAC;YAIzB,OAAO,YAAY,KAAK,CAAC,CAAC;QAC5B,CAAC,CACF,CAAC;QAGF,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAO1C,SAAS,uBAAuB,CAAC,GAAM,EAAE,YAA8B;YACrE,MAAM,MAAM,GAAQ,IAAI,UAAU,CAAI,CAAC,eAAe,EAAE,EAAE;gBACxD,YAAY,EAAE,CAAC;gBACf,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;gBACzD,OAAO,GAAG,EAAE;oBACV,QAAQ,CAAC,WAAW,EAAE,CAAC;oBAIvB,EAAE,YAAY,KAAK,CAAC,IAAI,iBAAiB,IAAI,uBAAuB,CAAC,WAAW,EAAE,CAAC;gBACrF,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}