import { type Tree } from '@nx/devkit';
import { type PackageCompatVersions, type PackageLatestVersions } from '../../utils/backward-compatible-versions';
export declare function getInstalledAngularVersion(tree: Tree): string;
export declare function getInstalledAngularMajorVersion(tree: Tree): number;
export declare function getInstalledAngularVersionInfo(tree: Tree): {
    version: string;
    major: number;
};
export declare function getInstalledPackageVersion(tree: Tree, pkgName: string): string | null;
export declare function getInstalledPackageVersionInfo(tree: Tree, pkgName: string): {
    major: number;
    version: string;
};
export declare function versions(tree: Tree): PackageLatestVersions | PackageCompatVersions;
