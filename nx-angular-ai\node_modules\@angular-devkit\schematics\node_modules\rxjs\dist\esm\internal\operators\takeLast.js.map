{"version": 3, "file": "takeLast.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/takeLast.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAE5C,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAyChE,MAAM,UAAU,QAAQ,CAAI,KAAa;IACvC,OAAO,KAAK,IAAI,CAAC;QACf,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;QACb,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;YAK7B,IAAI,MAAM,GAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,CAAC,KAAK,EAAE,EAAE;gBAER,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAGnB,KAAK,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YAC1C,CAAC,EACD,GAAG,EAAE;gBAGH,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;oBAC1B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACxB;gBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,CAAC,EAED,SAAS,EACT,GAAG,EAAE;gBAEH,MAAM,GAAG,IAAK,CAAC;YACjB,CAAC,CACF,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;AACT,CAAC"}