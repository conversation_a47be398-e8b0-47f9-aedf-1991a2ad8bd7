import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <a routerLink="/" class="flex-shrink-0 flex items-center">
              <img class="h-8 w-auto" src="/assets/logo.png" alt="Psychology Chat" />
              <span class="ml-2 text-xl font-bold text-gray-900">Psychology Chat</span>
            </a>
          </div>

          <!-- Navigation Links -->
          <div class="hidden md:flex items-center space-x-8">
            <a routerLink="/" 
               routerLinkActive="text-blue-600 border-b-2 border-blue-600"
               [routerLinkActiveOptions]="{exact: true}"
               class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              Home
            </a>
            <a routerLink="/pricing" 
               routerLinkActive="text-blue-600 border-b-2 border-blue-600"
               class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              Pricing
            </a>
            <a routerLink="/about" 
               routerLinkActive="text-blue-600 border-b-2 border-blue-600"
               class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              About
            </a>
          </div>

          <!-- Start Chat Button -->
          <div class="flex items-center">
            <a routerLink="/sign-in" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors">
              Start Chat
            </a>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden flex items-center">
            <button (click)="toggleMobileMenu()" 
                    class="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path *ngIf="!isMobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                <path *ngIf="isMobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Mobile menu -->
        <div *ngIf="isMobileMenuOpen" class="md:hidden">
          <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
            <a routerLink="/" 
               (click)="closeMobileMenu()"
               routerLinkActive="text-blue-600 bg-blue-50"
               [routerLinkActiveOptions]="{exact: true}"
               class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium">
              Home
            </a>
            <a routerLink="/pricing" 
               (click)="closeMobileMenu()"
               routerLinkActive="text-blue-600 bg-blue-50"
               class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium">
              Pricing
            </a>
            <a routerLink="/about" 
               (click)="closeMobileMenu()"
               routerLinkActive="text-blue-600 bg-blue-50"
               class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium">
              About
            </a>
            <a routerLink="/sign-in" 
               (click)="closeMobileMenu()"
               class="bg-blue-600 hover:bg-blue-700 text-white block px-3 py-2 text-base font-medium rounded-lg mt-4">
              Start Chat
            </a>
          </div>
        </div>
      </div>
    </nav>
  `,
  styleUrls: ['./navbar.component.scss']
})
export class NavbarComponent {
  isMobileMenuOpen = false;

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }
}
