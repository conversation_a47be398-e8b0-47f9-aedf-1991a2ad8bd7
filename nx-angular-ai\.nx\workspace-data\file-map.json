{"version": "6.0", "nxVersion": "20.6.0", "pathMappings": {}, "nxJsonPlugins": [{"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "fileMap": {"nonProjectFiles": [], "projectFileMap": {"nx-angular-ai": [{"file": ".giti<PERSON>re", "hash": "12598051590701509975"}, {"file": ".prettieri<PERSON>re", "hash": "5407418692668764289"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": ".vscode/extensions.json", "hash": "108759356477840841"}, {"file": ".vscode/settings.json", "hash": "7958163814861526821"}, {"file": "Dockerfile", "hash": "5004608181924306363"}, {"file": "README.md", "hash": "8547694039393956937"}, {"file": "api-specification.md", "hash": "13929825193292068201"}, {"file": "context.md", "hash": "4364404851742458412"}, {"file": "docker-compose.yml", "hash": "14040400434909957737"}, {"file": "eslint.config.mjs", "hash": "12526880816713913869", "deps": ["npm:@nx/eslint-plugin", "npm:eslint-plugin-unused-imports", "npm:@angular-eslint/eslint-plugin", "npm:@angular-eslint/eslint-plugin-template"]}, {"file": "jest.config.ts", "hash": "13328507328141560484"}, {"file": "jest.preset.js", "hash": "9430166341120122740", "deps": ["npm:@nx/jest"]}, {"file": "migrations.json", "hash": "3712336098738998037"}, {"file": "nginx.conf", "hash": "8482166704771749477"}, {"file": "nx.json", "hash": "7419752013685998571"}, {"file": "package-lock.json", "hash": "6857944579786259838"}, {"file": "package.json", "hash": "11893325916128384836", "deps": ["npm:@angular-devkit/build-angular", "npm:@angular-devkit/core", "npm:@angular-devkit/schematics", "npm:@angular/cli", "npm:@angular/compiler-cli", "npm:@angular/language-service", "npm:@eslint/js", "npm:@nx/angular", "npm:@nx/devkit", "npm:@nx/eslint", "npm:@nx/eslint-plugin", "npm:@nx/jest", "npm:@nx/js", "npm:@nx/playwright", "npm:@nx/web", "npm:@nx/workspace", "npm:@playwright/test", "npm:@schematics/angular", "npm:@swc-node/register", "npm:@swc/core", "npm:@swc/helpers", "npm:@types/jest", "npm:@types/node", "npm:@types/uuid", "npm:@typescript-eslint/utils", "npm:angular-eslint", "npm:autoprefixer", "npm:eslint", "npm:eslint-config-prettier", "npm:eslint-plugin-playwright", "npm:eslint-plugin-unused-imports", "npm:jest", "npm:jest-environment-jsdom", "npm:jest-preset-angular", "npm:nx", "npm:postcss", "npm:prettier", "npm:tailwindcss", "npm:ts-jest", "npm:ts-node", "npm:typescript", "npm:typescript-eslint", "npm:@angular/animations", "npm:@angular/common", "npm:@angular/compiler", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/platform-browser", "npm:@angular/platform-browser-dynamic", "npm:@angular/router", "npm:highlight.js", "npm:marked", "npm:rxjs", "npm:uuid", "npm:zone.js"]}, {"file": "project.json", "hash": "11730506139423639308"}, {"file": "proxy.conf.js", "hash": "10959895607708773743"}, {"file": "proxy.conf.json", "hash": "8469150945369929750"}, {"file": "public/favicon.ico", "hash": "9303420814833116677"}, {"file": "scripts/generate-version.js", "hash": "15690919946006394912"}, {"file": "src/app/app.component.html", "hash": "848395303240231261"}, {"file": "src/app/app.component.scss", "hash": "3244421341483603138"}, {"file": "src/app/app.component.ts", "hash": "5459059789888919916", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/common"]}, {"file": "src/app/app.config.ts", "hash": "7272319674090801257", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/common"]}, {"file": "src/app/app.routes.ts", "hash": "5444819985049831177", "deps": ["npm:@angular/router"]}, {"file": "src/app/components/about/about.component.scss", "hash": "10407695126112731382"}, {"file": "src/app/components/about/about.component.ts", "hash": "13625178650182793494", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "src/app/components/auth/sign-in/sign-in.component.css", "hash": "17653856896663973111"}, {"file": "src/app/components/auth/sign-in/sign-in.component.html", "hash": "5303786822639735666"}, {"file": "src/app/components/auth/sign-in/sign-in.component.spec.ts", "hash": "1140201652815053821", "deps": ["npm:@angular/core"]}, {"file": "src/app/components/auth/sign-in/sign-in.component.ts", "hash": "15596097310839840537", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@angular/router"]}, {"file": "src/app/components/auth/sign-up/sign-up.component.css", "hash": "11558186181659891281"}, {"file": "src/app/components/auth/sign-up/sign-up.component.html", "hash": "17563186831581841565"}, {"file": "src/app/components/auth/sign-up/sign-up.component.html.new", "hash": "17563186831581841565"}, {"file": "src/app/components/auth/sign-up/sign-up.component.spec.ts", "hash": "3341724609877509690", "deps": ["npm:@angular/core"]}, {"file": "src/app/components/auth/sign-up/sign-up.component.ts", "hash": "1911000147616425243", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@angular/router"]}, {"file": "src/app/components/chat-area/chat-area.component.html", "hash": "10548436734452774296"}, {"file": "src/app/components/chat-area/chat-area.component.scss", "hash": "18038204291798401433"}, {"file": "src/app/components/chat-area/chat-area.component.ts", "hash": "17513300954258948467", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:rxjs"]}, {"file": "src/app/components/chat-history/chat-history.component.html", "hash": "11899078140504817015"}, {"file": "src/app/components/chat-history/chat-history.component.scss", "hash": "8354181540751638455"}, {"file": "src/app/components/chat-history/chat-history.component.ts", "hash": "18183798403596089734", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "src/app/components/chat-layout/chat-layout.component.html", "hash": "18181051475920008207"}, {"file": "src/app/components/chat-layout/chat-layout.component.scss", "hash": "8354181540751638455"}, {"file": "src/app/components/chat-layout/chat-layout.component.ts", "hash": "17270111276480441691", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "src/app/components/landing/footer/footer.component.scss", "hash": "667797151766240923"}, {"file": "src/app/components/landing/footer/footer.component.ts", "hash": "8258856409863097226", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "src/app/components/landing/hero-banner/hero-banner.component.scss", "hash": "13767150843098229251"}, {"file": "src/app/components/landing/hero-banner/hero-banner.component.ts", "hash": "13238496746091915691", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "src/app/components/landing/landing.component.scss", "hash": "4802716653127747104"}, {"file": "src/app/components/landing/landing.component.ts", "hash": "6448352869149591496", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "src/app/components/landing/navbar/navbar.component.scss", "hash": "14028014279271923130"}, {"file": "src/app/components/landing/navbar/navbar.component.ts", "hash": "3133971748998682446", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "src/app/components/landing/transform-conversation/transform-conversation.component.scss", "hash": "9431991014095987450"}, {"file": "src/app/components/landing/transform-conversation/transform-conversation.component.ts", "hash": "9877232938149124121", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "src/app/components/landing/why-choose-us/why-choose-us.component.scss", "hash": "13360785569797916169"}, {"file": "src/app/components/landing/why-choose-us/why-choose-us.component.ts", "hash": "6332903208790726033", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "src/app/components/message/message.component.html", "hash": "8189022367262858387"}, {"file": "src/app/components/message/message.component.scss", "hash": "1531720513909609300"}, {"file": "src/app/components/message/message.component.ts", "hash": "13275053637528736534", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/platform-browser", "npm:marked"]}, {"file": "src/app/components/pricing/pricing.component.scss", "hash": "14087674077776541581"}, {"file": "src/app/components/pricing/pricing.component.ts", "hash": "9822894046696755050", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "src/app/components/source-documents/source-documents.component.html", "hash": "4050932721041798195"}, {"file": "src/app/components/source-documents/source-documents.component.scss", "hash": "1804304519650498703"}, {"file": "src/app/components/source-documents/source-documents.component.ts", "hash": "10734911255772477360", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "src/app/components/thinking-panel/thinking-panel.component.html", "hash": "16679315376495317558"}, {"file": "src/app/components/thinking-panel/thinking-panel.component.scss", "hash": "15887762790082013268"}, {"file": "src/app/components/thinking-panel/thinking-panel.component.ts", "hash": "4293579085587605237", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "src/app/guards/auth.guard.ts", "hash": "2058847631768473408", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "src/app/models/chat.interfaces.ts", "hash": "6125488854765951518"}, {"file": "src/app/models/enums.ts", "hash": "2451828937273938988"}, {"file": "src/app/sample/sample-flowise-response.json", "hash": "5356997904840197993"}, {"file": "src/app/services/auth.service.ts", "hash": "5831876055183936930", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs", "npm:@angular/router"]}, {"file": "src/app/services/chat.service.ts", "hash": "15881076762325961843", "deps": ["npm:@angular/core", "npm:rxjs", "npm:uuid", "npm:@angular/common"]}, {"file": "src/app/services/safepay.service.ts", "hash": "7150089664419415989", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "src/app/services/theme.service.ts", "hash": "7619774752882722738", "deps": ["npm:@angular/core"]}, {"file": "src/app/shared/ui/button/button.component.html", "hash": "7014306424735501027"}, {"file": "src/app/shared/ui/button/button.component.scss", "hash": "1305027153812358427"}, {"file": "src/app/shared/ui/button/button.component.ts", "hash": "17022218572207796059", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "src/app/shared/ui/text-input/text-input.component.html", "hash": "7053637925797504740"}, {"file": "src/app/shared/ui/text-input/text-input.component.html.new", "hash": "7053637925797504740"}, {"file": "src/app/shared/ui/text-input/text-input.component.scss", "hash": "3409793962191005175"}, {"file": "src/app/shared/ui/text-input/text-input.component.ts", "hash": "14706743625575962665", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms"]}, {"file": "src/app/shared/ui/textarea/textarea.component.html", "hash": "913786462032247608"}, {"file": "src/app/shared/ui/textarea/textarea.component.scss", "hash": "10830507117918369551"}, {"file": "src/app/shared/ui/textarea/textarea.component.ts", "hash": "14303181562281290589", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms"]}, {"file": "src/environment.prod.ts", "hash": "4586440916239903496"}, {"file": "src/environment.ts", "hash": "12903056504361147688"}, {"file": "src/index.html", "hash": "5938782399603409546"}, {"file": "src/main.ts", "hash": "16635586463787978962", "deps": ["npm:@angular/platform-browser"]}, {"file": "src/styles.scss", "hash": "7646996486658690597"}, {"file": "src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "src/version.ts", "hash": "11529040482884566644"}, {"file": "tailwind.config.js", "hash": "1909424865903598118"}, {"file": "test.json", "hash": "17180756627513635969"}, {"file": "theme/plugins.css", "hash": "15302073520880082828"}, {"file": "theme/style.css", "hash": "18383944577771255876"}, {"file": "todo.md", "hash": "1499066433182273977"}, {"file": "tsconfig.app.json", "hash": "16616923596177837167"}, {"file": "tsconfig.editor.json", "hash": "1055794200401717100"}, {"file": "tsconfig.json", "hash": "564402030144987495"}, {"file": "tsconfig.spec.json", "hash": "16287279946930924495"}], "e2e": [{"file": "e2e/eslint.config.mjs", "hash": "3980065308462174374", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "e2e/playwright.config.ts", "hash": "8712158940178849414", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "e2e/project.json", "hash": "9019460358862709142"}, {"file": "e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "e2e/tsconfig.json", "hash": "16791083829424454904"}]}}}