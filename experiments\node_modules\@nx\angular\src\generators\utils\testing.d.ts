import type { Tree } from '@nx/devkit';
import type { Schema as ApplicationOptions } from '../application/schema';
import type { Schema as HostOptions } from '../host/schema';
import type { Schema as LibraryOptions } from '../library/schema';
import type { Schema as RemoteOptions } from '../remote/schema';
export declare function generateTestApplication(tree: Tree, options: ApplicationOptions): Promise<void>;
export declare function generateTestHostApplication(tree: Tree, options: HostOptions): Promise<void>;
export declare function generateTestRemoteApplication(tree: Tree, options: RemoteOptions): Promise<void>;
export declare function generateTestLibrary(tree: Tree, options: LibraryOptions): Promise<void>;
export declare function createStorybookTestWorkspaceForLib(libName: string): Promise<Tree>;
